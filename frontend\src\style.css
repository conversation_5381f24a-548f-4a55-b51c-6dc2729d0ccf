@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gray-50 text-gray-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
    margin: 0;
    min-height: 100vh;
  }

  #app {
    @apply min-h-screen;
  }
}

@layer components {
  .gradient-bg {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  }

  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .audio-visualizer {
    @apply w-full h-16 bg-gray-100 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300;
  }

  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
  }

  .btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-6 rounded-lg transition-all duration-200 border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .card {
    @apply bg-white rounded-xl shadow-lg border border-gray-200 p-6;
  }

  .input-field {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
  }

  .status-success {
    @apply text-green-600 bg-green-50 border border-green-200 rounded-lg p-3;
  }

  .status-error {
    @apply text-red-600 bg-red-50 border border-red-200 rounded-lg p-3;
  }

  .status-info {
    @apply text-blue-600 bg-blue-50 border border-blue-200 rounded-lg p-3;
  }

  .loading-spinner {
    @apply animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600;
  }
}
