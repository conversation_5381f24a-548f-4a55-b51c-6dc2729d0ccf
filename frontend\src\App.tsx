import React, { useState, useEffect } from 'react';
import { Mic, Volume2, Setting<PERSON>, Info, AlertCircle } from 'lucide-react';
import VoiceCloneSection from './components/VoiceCloneSection';
import TTSSection from './components/TTSSection';
import StatusDisplay from './components/StatusDisplay';
import TipsAccordion from './components/TipsAccordion';
import VoiceCloningAPI from './services/api';

interface AppState {
  isSystemReady: boolean;
  systemStatus: string;
  currentVoiceInfo: string | null;
  sampleAudioId: string | null;
}

function App() {
  const [state, setState] = useState<AppState>({
    isSystemReady: false,
    systemStatus: 'Checking system...',
    currentVoiceInfo: null,
    sampleAudioId: null,
  });

  useEffect(() => {
    checkSystemHealth();
  }, []);

  const checkSystemHealth = async () => {
    try {
      const health = await VoiceCloningAPI.healthCheck();
      setState(prev => ({
        ...prev,
        isSystemReady: health.voice_system_ready,
        systemStatus: health.voice_system_ready 
          ? 'System ready for voice cloning' 
          : 'System initializing...'
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        isSystemReady: false,
        systemStatus: 'Backend server not available. Please start the Python backend.'
      }));
    }
  };

  const handleVoiceCloned = (voiceInfo: string, sampleAudioId: string) => {
    setState(prev => ({
      ...prev,
      currentVoiceInfo: voiceInfo,
      sampleAudioId: sampleAudioId,
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="gradient-bg text-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 py-8 sm:py-12">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <Mic className="w-10 h-10 sm:w-12 sm:h-12 mr-3 sm:mr-4" />
              <h1 className="text-3xl sm:text-4xl font-bold">Clonie</h1>
            </div>
            <p className="text-lg sm:text-xl text-blue-100">
              High-Fidelity Voice Cloning Studio
            </p>
            <p className="text-sm sm:text-base text-blue-200 mt-2">
              Professional voice cloning with pristine audio quality
            </p>
          </div>
        </div>
      </div>

      {/* System Status */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 py-4">
        <StatusDisplay
          isReady={state.isSystemReady}
          status={state.systemStatus}
          onRetry={checkSystemHealth}
        />
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 pb-12">
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 lg:gap-8">
          {/* Voice Cloning Section */}
          <div className="space-y-6">
            <div className="card">
              <div className="flex items-center mb-6">
                <Volume2 className="w-6 h-6 mr-3 text-blue-600" />
                <h2 className="text-2xl font-bold text-gray-800">
                  Step 1: Upload Voice Sample
                </h2>
              </div>
              
              <VoiceCloneSection
                isSystemReady={state.isSystemReady}
                onVoiceCloned={handleVoiceCloned}
                currentVoiceInfo={state.currentVoiceInfo}
                sampleAudioId={state.sampleAudioId}
              />
            </div>
          </div>

          {/* TTS Generation Section */}
          <div className="space-y-6">
            <div className="card">
              <div className="flex items-center mb-6">
                <Settings className="w-6 h-6 mr-3 text-blue-600" />
                <h2 className="text-2xl font-bold text-gray-800">
                  Step 2: Generate Speech
                </h2>
              </div>
              
              <TTSSection
                isSystemReady={state.isSystemReady}
                hasVoice={!!state.currentVoiceInfo}
              />
            </div>
          </div>
        </div>

        {/* Tips Section */}
        <div className="mt-12">
          <TipsAccordion />
        </div>

        {/* Footer */}
        <div className="mt-16 text-center text-gray-500">
          <div className="flex items-center justify-center mb-2">
            <Info className="w-4 h-4 mr-2" />
            <span className="text-sm">
              Powered by XTTS v2 • Built with React & FastAPI
            </span>
          </div>
          <p className="text-xs">
            High-fidelity voice cloning with minimal processing for natural sound
          </p>
        </div>
      </div>
    </div>
  );
}

export default App;
