"""
Professional Voice Cloning & Text-to-Speech Program - REFINED VERSION
Author: AI Assistant
Description: High-fidelity voice cloning with minimal processing for natural sound
Features: Accurate voice cloning, gentle enhancement, quality preservation
"""

import os
import sys
import warnings
warnings.filterwarnings("ignore")

print("""
╔═══════════════════════════════════════════════════════════════╗
║        High-Fidelity Voice Cloning & TTS Studio v4.0          ║
╠═══════════════════════════════════════════════════════════════╣
║   Initializing system with quality-first approach...          ║
╚═══════════════════════════════════════════════════════════════╝
""")

# Core dependencies only
def check_core_dependencies():
    core_packages = [
        ('torch', 'torch'),
        ('torchaudio', 'torchaudio'), 
        ('TTS', 'TTS'),
        ('gradio', 'gradio'),
        ('numpy', 'numpy'),
        ('scipy', 'scipy'),
        ('librosa', 'librosa'),
        ('soundfile', 'soundfile')
    ]
    
    missing = []
    for package_name, import_name in core_packages:
        try:
            __import__(import_name)
            print(f"✓ {package_name} is installed")
        except ImportError:
            missing.append(package_name)
            print(f"✗ {package_name} is missing")
    
    if missing:
        print(f"\n📦 Installing core packages: {', '.join(missing)}")
        for package in missing:
            os.system(f"{sys.executable} -m pip install {package}")
    
    return len(missing) == 0

check_core_dependencies()

# Import core modules
import torch
import torchaudio
import gradio as gr
import numpy as np
import tempfile
import librosa
import soundfile as sf
from scipy import signal
from scipy.signal import butter, filtfilt
from scipy.io import wavfile

# Fix for PyTorch compatibility
torch.serialization.add_safe_globals(['TTS.tts.configs.xtts_config.XttsConfig'])

# Monkey patch for torch.load
original_load = torch.load
def patched_load(*args, **kwargs):
    kwargs['weights_only'] = False
    return original_load(*args, **kwargs)
torch.load = patched_load

# Import TTS
from TTS.api import TTS

class HighFidelityAudioProcessor:
    """Minimal, high-quality audio processing to preserve voice fidelity"""
    
    def __init__(self):
        self.target_sr = 24000  # XTTS native sample rate
        
    def prepare_reference_audio(self, audio_path, enhance=False):
        """Prepare reference audio with minimal processing"""
        print("📊 Analyzing reference audio...")
        
        # Load at native sample rate
        audio, sr = librosa.load(audio_path, sr=None, mono=True)
        
        # Only resample if necessary
        if sr != self.target_sr:
            print(f"Resampling from {sr}Hz to {self.target_sr}Hz...")
            audio = librosa.resample(audio, orig_sr=sr, target_sr=self.target_sr)
            sr = self.target_sr
        
        # Gentle normalization only
        audio = self.normalize_audio(audio)
        
        if enhance:
            # Very gentle enhancement only if requested
            audio = self.gentle_enhance(audio, sr)
        
        # Save
        temp_path = tempfile.mktemp(suffix='.wav')
        sf.write(temp_path, audio, sr, subtype='PCM_16')
        
        return temp_path
    
    def normalize_audio(self, audio):
        """Gentle normalization to prevent clipping"""
        max_val = np.max(np.abs(audio))
        if max_val > 0:
            # Normalize to 95% to leave headroom
            audio = audio * (0.95 / max_val)
        return audio
    
    def gentle_enhance(self, audio, sr):
        """Very subtle enhancement - optional"""
        # Gentle high-pass to remove only subsonic frequencies
        nyquist = sr / 2
        low_cutoff = 50 / nyquist  # Only remove below 50Hz
        
        if low_cutoff < 1.0:
            b, a = butter(2, low_cutoff, btype='high')
            audio = filtfilt(b, a, audio)
        
        return audio
    
    def remove_silence(self, audio, sr, threshold_db=40):
        """Remove only long silences, preserve natural pauses"""
        intervals = librosa.effects.split(audio, top_db=threshold_db)
        
        if len(intervals) > 0:
            # Keep all audio segments with natural timing
            result = []
            for start, end in intervals:
                result.append(audio[start:end])
                # Add tiny gap between segments
                if len(result) > 1:
                    result.append(np.zeros(int(0.05 * sr)))
            
            return np.concatenate(result) if result else audio
        return audio
    
    def process_generated_audio(self, audio_path, remove_silence_flag=False):
        """Minimal post-processing to preserve quality"""
        print("🎵 Applying minimal quality refinement...")
        
        # Load audio
        audio, sr = librosa.load(audio_path, sr=None, mono=True)
        
        # Only normalize
        audio = self.normalize_audio(audio)
        
        # Remove silence only if requested
        if remove_silence_flag:
            audio = self.remove_silence(audio, sr)
        
        # Gentle fade in/out to prevent clicks (2ms)
        fade_samples = int(0.002 * sr)
        if len(audio) > fade_samples * 2:
            audio[:fade_samples] *= np.linspace(0, 1, fade_samples)
            audio[-fade_samples:] *= np.linspace(1, 0, fade_samples)
        
        # Save
        output_path = tempfile.mktemp(suffix='.wav')
        sf.write(output_path, audio, sr, subtype='PCM_16')
        
        return output_path

class HighFidelityVoiceCloning:
    def __init__(self):
        """Initialize High-Fidelity Voice Cloning System"""
        print("🚀 Initializing High-Fidelity Voice Cloning...")
        
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"Using device: {self.device}")
        
        # Initialize audio processor
        self.audio_processor = HighFidelityAudioProcessor()
        
        try:
            # Load XTTS with optimal settings
            print("Loading XTTS v2 model...")
            self.tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2", gpu=(self.device == "cuda"))
            
            # Configure for quality
            if hasattr(self.tts, 'synthesizer') and hasattr(self.tts.synthesizer, 'tts_model'):
                config = getattr(self.tts.synthesizer.tts_model, 'config', None)
                if config:
                    # Optimal settings for quality
                    config.temperature = 0.7  # Balanced
                    config.length_penalty = 1.0
                    config.repetition_penalty = 5.0  # Reduce repetition
                    config.top_k = 50
                    config.top_p = 0.9  # Slightly higher for naturalness
                    config.do_sample = True
                    print("✅ Model configured for optimal quality")
            
            print("✅ Model loaded successfully!")
            
        except Exception as e:
            print(f"Error loading model: {e}")
            raise
        
        self.current_voice_path = None
        self.processed_voice_path = None
        self.sample_text = "Hello! This is a test of the voice cloning system. The quality should sound natural and clear."
        
    def clone_voice(self, audio_file, enhancement_level="none"):
        """Clone voice with focus on quality preservation"""
        try:
            if audio_file is None:
                return None, None, "Please upload an audio file first!"
            
            print(f"\n{'='*50}")
            print(f"🎯 Starting high-fidelity voice cloning...")
            print(f"Enhancement: {enhancement_level}")
            print(f"{'='*50}\n")
            
            # Process reference with minimal changes
            apply_enhancement = enhancement_level != "none"
            processed_audio = self.audio_processor.prepare_reference_audio(
                audio_file, 
                enhance=apply_enhancement
            )
            
            self.processed_voice_path = processed_audio
            self.current_voice_path = audio_file
            
            # Generate sample with high quality
            print("🎤 Generating voice sample...")
            sample_output = self._generate_high_quality_audio(
                self.sample_text, 
                processed_audio,
                quality_preset="high"
            )
            
            if sample_output:
                # Minimal post-processing
                sample_final = self.audio_processor.process_generated_audio(
                    sample_output,
                    remove_silence_flag=False
                )
                
                # Get audio info
                info = sf.info(audio_file)
                duration = info.duration
                
                info_text = f"📊 Voice Profile:\n"
                info_text += f"• Duration: {duration:.1f}s\n"
                info_text += f"• Sample Rate: {info.samplerate}Hz\n"
                info_text += f"• Processing: {'Minimal Enhancement' if apply_enhancement else 'None (Pure)'}\n"
                info_text += f"• Quality: Maximum Fidelity"
                
                return sample_final, info_text, "✅ Voice cloned successfully with high fidelity!"
            else:
                return None, None, "❌ Error during voice cloning"
                
        except Exception as e:
            print(f"Error: {str(e)}")
            return None, None, f"❌ Error: {str(e)}"
    
    def _generate_high_quality_audio(self, text, reference_audio, quality_preset="high"):
        """Generate audio with maximum quality settings"""
        try:
            # Create output file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as tmp_file:
                output_path = tmp_file.name
            
            print(f"🎙️ Synthesizing speech...")
            
            # Use best settings based on preset
            if quality_preset == "high":
                # Best quality settings
                self.tts.tts_to_file(
                    text=text,
                    speaker_wav=reference_audio,
                    language="en",
                    file_path=output_path,
                    split_sentences=True  # Better handling of longer texts
                )
            else:
                # Standard synthesis
                self.tts.tts_to_file(
                    text=text,
                    speaker_wav=reference_audio,
                    language="en",
                    file_path=output_path
                )
            
            return output_path
                
        except Exception as e:
            print(f"Synthesis error: {str(e)}")
            # Fallback method
            try:
                wav = self.tts.tts(
                    text=text,
                    speaker_wav=reference_audio,
                    language="en"
                )
                wavfile.write(output_path, 24000, (wav * 32767).astype(np.int16))
                return output_path
            except:
                return None
    
    def generate_tts(self, text, quality_mode="high", remove_silence=False):
        """Generate TTS with quality focus"""
        try:
            if not self.current_voice_path:
                return None, "❌ Please clone a voice first!"
            
            if not text.strip():
                return None, "❌ Please enter some text!"
            
            print(f"\n{'='*50}")
            print(f"🎯 Generating high-fidelity speech...")
            print(f"Quality mode: {quality_mode}")
            print(f"{'='*50}\n")
            
            # Use processed voice
            reference = self.processed_voice_path or self.current_voice_path
            
            # Clean text
            text = text.strip()
            
            # Generate with high quality
            output_path = self._generate_high_quality_audio(
                text, 
                reference,
                quality_preset=quality_mode
            )
            
            if output_path and os.path.exists(output_path):
                # Minimal post-processing
                final_output = self.audio_processor.process_generated_audio(
                    output_path,
                    remove_silence_flag=remove_silence
                )
                
                # Get stats
                duration = librosa.get_duration(filename=final_output)
                word_count = len(text.split())
                
                # Cleanup
                if output_path != final_output:
                    os.unlink(output_path)
                
                return final_output, f"✅ Generated {word_count} words | {duration:.1f}s | High Fidelity"
            else:
                return None, "❌ Error generating speech"
                
        except Exception as e:
            print(f"TTS error: {str(e)}")
            return None, f"❌ Error: {str(e)}"

def create_interface():
    """Create clean, professional interface"""
    try:
        vcs = HighFidelityVoiceCloning()
    except Exception as e:
        print(f"Failed to initialize: {e}")
        raise
    
    custom_css = """
    .gradio-container {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
    }
    .main-header {
        text-align: center;
        padding: 2rem;
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        border-radius: 12px;
        margin-bottom: 2rem;
    }
    .section-header {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: #1e3c72;
    }
    """
    
    with gr.Blocks(title="High-Fidelity Voice Cloning", theme=gr.themes.Base(), css=custom_css) as app:
        gr.HTML("""
        <div class="main-header">
            <h1>🎙️ High-Fidelity Voice Cloning Studio</h1>
            <p>Professional voice cloning with pristine audio quality</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                gr.Markdown("### 📤 Step 1: Upload Voice Sample")
                
                audio_input = gr.Audio(
                    label="Upload Reference Voice",
                    type="filepath"
                )
                
                enhancement_level = gr.Radio(
                    choices=["none", "minimal", "gentle"],
                    value="none",
                    label="Enhancement Level",
                    info="'none' preserves original quality best"
                )
                
                clone_btn = gr.Button("🎯 Clone Voice", variant="primary", size="lg")
                
                gr.Markdown("### 🎧 Cloned Voice Sample")
                
                sample_audio = gr.Audio(
                    label="Voice Sample",
                    type="filepath",
                    interactive=False
                )
                
                voice_info = gr.Textbox(
                    label="Voice Information",
                    lines=5,
                    interactive=False
                )
                
                clone_status = gr.Textbox(
                    label="Status",
                    interactive=False
                )
                
            with gr.Column(scale=1):
                gr.Markdown("### ✍️ Step 2: Generate Speech")
                
                text_input = gr.Textbox(
                    label="Enter Text",
                    placeholder="Type or paste your text here...",
                    lines=10
                )
                
                with gr.Row():
                    quality_mode = gr.Radio(
                        choices=["high", "standard"],
                        value="high",
                        label="Quality Mode"
                    )
                    
                    remove_silence = gr.Checkbox(
                        label="Remove Long Silences",
                        value=False
                    )
                
                with gr.Row():
                    generate_btn = gr.Button("🎯 Generate Speech", variant="primary", size="lg")
                    clear_btn = gr.Button("🗑️ Clear", variant="secondary")
                
                gr.Markdown("### 💾 Generated Speech")
                
                output_audio = gr.Audio(
                    label="Output (Right-click to download)",
                    type="filepath",
                    interactive=False
                )
                
                generate_status = gr.Textbox(
                    label="Generation Info",
                    interactive=False
                )
        
        # Tips
        with gr.Accordion("💡 Tips for Best Quality", open=False):
            gr.Markdown("""
            **For highest quality output:**
            - Use clear audio recordings without background noise
            - Upload at least 10-30 seconds of speech
            - Use "none" enhancement to preserve original voice quality
            - Keep text under 500 words per generation
            - High-quality WAV files work best
            
            **About enhancement levels:**
            - **None**: Pure cloning - preserves exact voice characteristics
            - **Minimal**: Only removes sub-50Hz frequencies
            - **Gentle**: Minimal + very light cleanup
            
            The system is optimized for natural, high-fidelity output without heavy processing.
            """)
        
        # Event handlers
        clone_btn.click(
            fn=vcs.clone_voice,
            inputs=[audio_input, enhancement_level],
            outputs=[sample_audio, voice_info, clone_status]
        )
        
        generate_btn.click(
            fn=vcs.generate_tts,
            inputs=[text_input, quality_mode, remove_silence],
            outputs=[output_audio, generate_status]
        )
        
        clear_btn.click(
            fn=lambda: "",
            outputs=[text_input]
        )
    
    return app

def main():
    """Main function"""
    print("""
    ╔═══════════════════════════════════════════════════════════════╗
    ║       High-Fidelity Voice Cloning & TTS Studio v4.0           ║
    ╠═══════════════════════════════════════════════════════════════╣
    ║   🎙️  Pristine voice quality preservation                     ║
    ║   🎵  Natural, unprocessed sound                              ║
    ║   ⚡  Optimized for clarity and fidelity                      ║
    ╚═══════════════════════════════════════════════════════════════╝
    """)
    
    try:
        app = create_interface()
        
        print("\n✨ Starting High-Fidelity Voice Cloning Studio...")
        print("📱 Opening in your browser...")
        print("🛑 Press CTRL+C to stop\n")
        
        app.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=True,
            inbrowser=True
        )
    except KeyboardInterrupt:
        print("\n👋 Shutting down...")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()