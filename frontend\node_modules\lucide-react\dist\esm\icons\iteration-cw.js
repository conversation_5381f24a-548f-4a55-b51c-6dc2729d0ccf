/**
 * @license lucide-react v0.533.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M4 10a8 8 0 1 1 8 8H4", key: "svv66n" }],
  ["path", { d: "m8 22-4-4 4-4", key: "6g7gki" }]
];
const IterationCw = createLucideIcon("iteration-cw", __iconNode);

export { __iconNode, IterationCw as default };
//# sourceMappingURL=iteration-cw.js.map
