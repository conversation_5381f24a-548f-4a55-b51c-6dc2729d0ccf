/**
 * @license lucide-react v0.533.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m14.499 12.501-8.88 8.879a1 1 0 0 1-3.001-3L11.5 9.5", key: "19sw30" }],
  ["path", { d: "m16 16 6-6", key: "vzrcl6" }],
  ["path", { d: "m21 11-8-8", key: "z4y7zo" }],
  ["path", { d: "m8 8 6-6", key: "18bi4p" }],
  ["path", { d: "m9 7 8 8", key: "5jnvq1" }]
];
const Gavel = createLucideIcon("gavel", __iconNode);

export { __iconNode, Gavel as default };
//# sourceMappingURL=gavel.js.map
