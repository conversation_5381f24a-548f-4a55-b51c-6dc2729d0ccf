/**
 * @license lucide-react v0.533.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m16 16-4 4-4-4", key: "3dv8je" }],
  ["path", { d: "M3 12h18", key: "1i2n21" }],
  ["path", { d: "m8 8 4-4 4 4", key: "2bscm2" }]
];
const SeparatorHorizontal = createLucideIcon("separator-horizontal", __iconNode);

export { __iconNode, SeparatorHorizontal as default };
//# sourceMappingURL=separator-horizontal.js.map
